import { createHash } from 'crypto';
import { 
  SyncManifest, 
  SyncItem as SyncItemType, 
  Changes, 
  ConflictItem,
  ManifestItem,
  LocalItem,
  DeletionRecord
} from './types';
import { ManifestManager } from './manifest-manager';
import { dbAll } from '../../database/database-api';

// Local type definitions
interface SyncItem {
  id: string;
  type: 'book' | 'folder' | 'note';
  hash: string;
  lastModified: string;
  metadata?: any;
}

interface ItemsByType {
  books: SyncItem[];
  folders: SyncItem[];
  notes: SyncItem[];
}

interface LocalItemsByType {
  books: LocalItem[];
  folders: LocalItem[];
  notes: LocalItem[];
}

interface ManifestItemsByType {
  books: ManifestItem[];
  folders: ManifestItem[];
  notes: ManifestItem[];
}

export class ChangeDetector {
  private manifestManager: ManifestManager;
  private pendingDeletions: Array<{id: string, type: 'book' | 'folder' | 'note'}> = [];

  constructor() {
    this.manifestManager = new ManifestManager();
  }

  /**
   * Main comparison method that orchestrates the change detection process
   */
  async compareStates(manifest: SyncManifest, syncPath: string): Promise<Changes> {
    // Reset pending deletions for this comparison
    this.pendingDeletions = [];
    // Get current database state
    const dbBooks = await this.getDbBooks();
    const dbFolders = await this.getDbFolders();
    const dbNotes = await this.getDbNotes();

    // Get last sync hashes from sync_items table
    const syncHashes = await this.getSyncHashes();

    // Categorize manifest items by type
    const manifestByType = this.categorizeByType(manifest.items);
    const dbByType: ItemsByType = {
      books: dbBooks,
      folders: dbFolders,
      notes: dbNotes
    };

    // Detect changes for each type
    const toImport: ManifestItemsByType = {
      books: [],
      folders: [],
      notes: []
    };
    const toExport: LocalItemsByType = {
      books: [],
      folders: [],
      notes: []
    };
    const conflicts: ConflictItem[] = [];

    // Process each item type in order of hierarchy
    const itemTypes: Array<keyof ItemsByType> = ['books', 'folders', 'notes'];
    
    for (const type of itemTypes) {
      const manifestItems = manifestByType[type];
      const dbItems = dbByType[type];

      // Find items to import (in manifest but not in DB)
      const itemsToImport = this.findItemsToImport(manifestItems, dbItems, manifest.deletions);
      toImport[type] = itemsToImport;

      // Find items to export (in DB but not in manifest)
      const itemsToExport = this.findItemsToExport(dbItems, manifestItems, syncHashes);
      toExport[type] = itemsToExport;

      // Find conflicts (different hashes on both sides)
      const conflictItems = this.findConflicts(manifestItems, dbItems, syncHashes);
      conflicts.push(...conflictItems);
    }

    // Process deletions
    this.processDeletions(manifest.deletions, dbByType);

    return {
      toImport,
      toExport,
      conflicts,
      toDelete: this.pendingDeletions
    };
  }


  /**
   * Find items that exist in manifest but not in database
   */
  private findItemsToImport(
    manifestItems: ManifestItem[], 
    dbItems: SyncItem[],
    deletions: DeletionRecord[]
  ): ManifestItem[] {
    const dbIds = new Set(dbItems.map(item => item.id));
    const deletionSet = new Set(deletions.map(d => d.id));
    
    return manifestItems.filter(item => 
      !dbIds.has(item.id) && !deletionSet.has(item.id)
    );
  }

  /**
   * Find items that exist in database but not in manifest
   */
  private findItemsToExport(
    dbItems: SyncItem[], 
    manifestItems: ManifestItem[],
    syncHashes: Map<string, string>
  ): LocalItem[] {
    const manifestIds = new Set(manifestItems.map(item => item.id));
    
    return dbItems
      .filter(item => {
        // Item not in manifest
        if (!manifestIds.has(item.id)) {
          return true;
        }
        
        // Item in manifest but local version is newer
        const compositeKey = `${item.type}_${item.id}`;
        const lastSyncHash = syncHashes.get(compositeKey);
        if (lastSyncHash && item.hash !== lastSyncHash) {
          return true;
        }
        
        return false;
      })
      .map(item => {
        // Extract numeric ID from prefixed format (e.g., "book_1" -> 1)
        const numericId = parseInt(item.id.split('_')[1]);
        return {
          id: numericId,
          type: item.type,
          name: item.metadata?.title || item.metadata?.name || '',
          content: item.metadata,
          modified: item.lastModified
        };
      });
  }

  /**
   * Find items that exist in both but have different hashes
   */
  private findConflicts(
    manifestItems: ManifestItem[], 
    dbItems: SyncItem[],
    syncHashes: Map<string, string>
  ): ConflictItem[] {
    const conflicts: ConflictItem[] = [];
    const manifestMap = new Map(manifestItems.map(item => [item.id, item]));
    
    for (const dbItem of dbItems) {
      const manifestItem = manifestMap.get(dbItem.id);
      if (!manifestItem) continue;
      
      // Check if both sides have changed since last sync
      const compositeKey = `${dbItem.type}_${dbItem.id}`;
      const lastSyncHash = syncHashes.get(compositeKey);
      const dbChanged = lastSyncHash ? dbItem.hash !== lastSyncHash : true;
      const manifestChanged = lastSyncHash ? manifestItem.hash !== lastSyncHash : true;
      
      if (dbChanged && manifestChanged && dbItem.hash !== manifestItem.hash) {
        conflicts.push({
          id: dbItem.id,
          type: dbItem.type,
          local: {
            id: parseInt(dbItem.id.split('_')[1]),
            type: dbItem.type,
            name: dbItem.metadata?.title || dbItem.metadata?.name || '',
            content: dbItem.metadata,
            modified: dbItem.lastModified
          },
          remote: manifestItem
        });
      }
    }
    
    return conflicts;
  }

  /**
   * Handle deleted items
   */
  private processDeletions(deletions: DeletionRecord[], dbItems: ItemsByType): void {
    const deletionSet = new Set(deletions.map(d => d.id));
    
    // Mark items for deletion in database
    for (const deletion of deletions) {
      const id = deletion.id;
      // Check if item still exists in database
      const bookExists = dbItems.books.some(book => book.id === id);
      const folderExists = dbItems.folders.some(folder => folder.id === id);
      const noteExists = dbItems.notes.some(note => note.id === id);
      
      if (bookExists) {
        this.markForDeletion(id, 'book');
      } else if (folderExists) {
        this.markForDeletion(id, 'folder');
      } else if (noteExists) {
        this.markForDeletion(id, 'note');
      }
    }
  }

  /**
   * Categorize items by type
   */
  private categorizeByType(items: ManifestItem[]): ManifestItemsByType {
    const categorized: ManifestItemsByType = {
      books: [],
      folders: [],
      notes: []
    };
    
    for (const item of items) {
      switch (item.type) {
        case 'book':
          categorized.books.push(item);
          break;
        case 'folder':
          categorized.folders.push(item);
          break;
        case 'note':
          categorized.notes.push(item);
          break;
      }
    }
    
    return categorized;
  }

  /**
   * Get all books from database with sync metadata
   */
  private async getDbBooks(): Promise<SyncItem[]> {
    const query = `
      SELECT id, title, author, isbn, cover_url as cover_image, created_at, updated_at
      FROM books
      ORDER BY created_at DESC
    `;
    
    const books = await dbAll<any>(query);
    
    return books.map(book => ({
      id: `book_${book.id}`,
      type: 'book' as const,
      hash: this.generateHash(book),
      lastModified: new Date(book.updated_at || book.created_at).toISOString(),
      metadata: {
        title: book.title,
        author: book.author,
        isbn: book.isbn,
        coverImage: book.cover_image
      }
    }));
  }

  /**
   * Get all folders from database with sync metadata
   */
  private async getDbFolders(): Promise<SyncItem[]> {
    const query = `
      SELECT id, name, parent_id, book_id, color, created_at, updated_at
      FROM folders
      ORDER BY name ASC
    `;
    
    const folders = await dbAll<any>(query);
    
    return folders.map(folder => ({
      id: `folder_${folder.id}`,
      type: 'folder' as const,
      hash: this.generateHash(folder),
      lastModified: new Date(folder.updated_at || folder.created_at).toISOString(),
      metadata: {
        name: folder.name,
        parentId: folder.parent_id,
        bookId: folder.book_id,
        color: folder.color
      }
    }));
  }

  /**
   * Get all notes from database with sync metadata
   */
  private async getDbNotes(): Promise<SyncItem[]> {
    const query = `
      SELECT id, title, content, book_id, folder_id, color, created_at, updated_at
      FROM notes
      ORDER BY created_at DESC
    `;
    
    const notes = await dbAll<any>(query);
    
    return notes.map(note => ({
      id: `note_${note.id}`,
      type: 'note' as const,
      hash: this.generateHash(note),
      lastModified: new Date(note.updated_at || note.created_at).toISOString(),
      metadata: {
        title: note.title,
        bookId: note.book_id,
        folderId: note.folder_id,
        color: note.color
      }
    }));
  }

  /**
   * Get last sync hashes from sync_items table
   */
  private async getSyncHashes(): Promise<Map<string, string>> {
    try {
      const query = `
        SELECT 
          item_type || '_' || item_id as composite_id,
          sync_hash as last_hash
        FROM sync_state
      `;
      
      const syncItems = await dbAll<any>(query);
      return new Map(syncItems.map(item => [
        item.composite_id,
        item.last_hash
      ]));
    } catch (error) {
      // If table doesn't exist yet, return empty map
      console.log('sync_state table not found, returning empty hash map');
      return new Map();
    }
  }

  /**
   * Mark an item for deletion
   */
  private markForDeletion(id: string, type: 'book' | 'folder' | 'note'): void {
    this.pendingDeletions.push({ id, type });
    console.log(`Item ${id} (${type}) marked for deletion`);
  }

  /**
   * Generate hash for an item
   */
  private generateHash(item: any): string {
    // Create a consistent string representation of the item
    const relevant = {
      ...item,
      created_at: undefined,
      updated_at: undefined
    };
    
    const content = JSON.stringify(relevant, Object.keys(relevant).sort());
    return createHash('sha256').update(content).digest('hex');
  }
}