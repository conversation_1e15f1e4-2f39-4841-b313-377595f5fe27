/**
 * Type definitions for the unified sync system
 */

/**
 * Error codes for sync operations
 */
export enum ErrorCode {
  FILE_READ_ERROR = 'FILE_READ_ERROR',
  FILE_WRITE_ERROR = 'FILE_WRITE_ERROR',
  PATH_ERROR = 'PATH_ERROR',
  SYNC_ERROR = 'SYNC_ERROR',
  SYNC_IN_PROGRESS = 'SYNC_IN_PROGRESS',
  IMPORT_ERROR = 'IMPORT_ERROR',
  EXPORT_ERROR = 'EXPORT_ERROR',
  CONFLICT_RESOLUTION_ERROR = 'CONFLICT_RESOLUTION_ERROR'
}

/**
 * Custom error class for sync operations
 */
export class SyncError extends Error {
  constructor(public code: ErrorCode, message: string) {
    super(message);
    this.name = 'SyncError';
  }
}

/**
 * Sync note structure
 */
export interface SyncNote {
  content: string;
  metadata: any;
}

/**
 * Book metadata structure
 */
export interface SyncBookMeta {
  id: number;
  title: string;
  author: string;
  isbn?: string;
  publicationYear?: string;
  pageCount?: number;
  description?: string;
  coverImage?: string;
  coverImageId?: string;
  googleBooksId?: string;
  createdAt: string;
  updatedAt: string;
  rating?: number;
  status?: 'to-read' | 'reading' | 'completed';
  tags?: string[];
}

/**
 * Folder structure for directory listings
 */
export interface SyncFolderStructure {
  path: string;
  name: string;
  type: 'folder' | 'file';
  children?: SyncFolderStructure[];
}

/**
 * Sync manifest structure that tracks all items and their state
 * Stored as sync-manifest.json in the sync directory
 */
export interface SyncManifest {
  /** Manifest version for future compatibility */
  version: number;
  /** Unique device identifier */
  deviceId: string;
  /** Timestamp of last successful sync */
  lastSync: string;
  /** Flat array of all synced items with type discrimination */
  items: ManifestItem[];
  /** Track deleted items for proper sync */
  deletions: DeletionRecord[];
}

/**
 * Individual item in the sync manifest
 * Represents a book, folder, or note with its metadata
 */
export interface ManifestItem {
  /** Unique identifier for the item */
  id: string;
  /** Type discriminator for the item */
  type: 'book' | 'folder' | 'note';
  /** Display name of the item */
  name: string;
  /** Relative path within the sync directory */
  path: string;
  /** Content hash for change detection */
  hash: string;
  /** Last modification timestamp */
  modified: string;
  /** Last update timestamp (for compatibility) */
  updated_at?: string;
  /** Device that last modified this item */
  device_id?: string;
  /** Additional metadata */
  metadata?: Record<string, any>;
  /** Relationship mappings for notes and folders */
  relationships?: {
    /** ID of the book this item belongs to (for notes/folders) */
    bookId?: string;
    /** ID of the parent folder (for notes) */
    folderId?: string;
    /** ID of the parent folder (for subfolders) */
    parentId?: string;
  };
}

/**
 * Record of a deleted item for sync purposes
 */
export interface DeletionRecord {
  /** ID of the deleted item */
  id: string;
  /** Type of the deleted item */
  type: 'book' | 'folder' | 'note';
  /** Timestamp when the item was deleted */
  deletedAt: string;
  /** Path where the item was located */
  path: string;
}

/**
 * Changes detected during sync comparison
 * Categorizes items that need to be imported, exported, or have conflicts
 */
export interface Changes {
  /** Items that need to be imported from sync directory to local */
  toImport: {
    books: ManifestItem[];
    folders: ManifestItem[];
    notes: ManifestItem[];
  };
  /** Items that need to be exported from local to sync directory */
  toExport: {
    books: LocalItem[];
    folders: LocalItem[];
    notes: LocalItem[];
  };
  /** Items with conflicts that need resolution */
  conflicts: ConflictItem[];
  /** Items that need to be deleted from local database */
  toDelete: Array<{
    id: string;
    type: 'book' | 'folder' | 'note';
  }>;
}

/**
 * Local database item structure for export
 */
export interface LocalItem {
  /** Database ID */
  id: number;
  /** Item type */
  type: 'book' | 'folder' | 'note';
  /** Display name */
  name: string;
  /** Content or metadata depending on type */
  content?: any;
  /** Last modification timestamp */
  modified: string;
  /** Relationship IDs */
  bookId?: number;
  folderId?: number;
}

/**
 * Conflict item requiring resolution
 */
export interface ConflictItem {
  /** Item identifier */
  id: string;
  /** Item type */
  type: 'book' | 'folder' | 'note';
  /** Local version of the item */
  local: LocalItem;
  /** Remote version from manifest */
  remote: ManifestItem;
  /** Suggested resolution strategy */
  resolution?: 'local' | 'remote' | 'merge';
}

/**
 * Result of a sync operation
 * Contains counts and details of the sync process
 */
export interface SyncResult {
  /** Whether the sync completed successfully */
  success: boolean;
  /** Number of items imported from sync directory */
  imported: {
    books: number;
    folders: number;
    notes: number;
  };
  /** Number of items exported to sync directory */
  exported: {
    books: number;
    folders: number;
    notes: number;
  };
  /** Number of items deleted from local database */
  deleted: {
    books: number;
    folders: number;
    notes: number;
  };
  /** Conflicts that were detected and/or resolved */
  conflicts: ConflictItem[];
  /** Any errors that occurred during sync */
  errors: string[];
  /** Timestamp when sync completed */
  timestamp: string;
  /** Total number of items imported (for backward compatibility) */
  itemsImported?: number;
  /** Total number of items exported (for backward compatibility) */
  itemsExported?: number;
  /** Duration of sync operation in milliseconds */
  duration?: number;
}

/**
 * Sync state database table structure
 * Tracks sync metadata for items
 */
export interface SyncState {
  /** Database ID */
  id: number;
  /** Type of item being tracked */
  item_type: 'book' | 'folder' | 'note';
  /** ID of the item in its respective table */
  item_id: number;
  /** Content hash for change detection */
  sync_hash: string;
  /** Last sync timestamp */
  last_synced: string;
  /** Sync status */
  sync_status: 'synced' | 'pending' | 'conflict';
  /** Created timestamp */
  created_at: string;
  /** Updated timestamp */
  updated_at: string;
}

/**
 * Sync item database table structure
 * Maps local items to sync paths
 */
export interface SyncItem {
  /** Database ID */
  id: number;
  /** Type of synced item */
  type: 'book' | 'folder' | 'note';
  /** ID in the respective table */
  item_id: number;
  /** Path in sync directory */
  path: string;
  /** Whether item is active in sync */
  is_active: boolean;
  /** Content hash for change detection */
  hash?: string;
  /** Sync ID for tracking */
  syncId?: string;
  /** Item title */
  title?: string;
  /** Additional metadata */
  metadata?: Record<string, any>;
  /** Created timestamp */
  created_at: string;
  /** Updated timestamp */
  updated_at: string;
}

/**
 * Book metadata structure for .book-meta.json files
 * Contains book information and cover image reference
 */
export interface BookMetadata {
  /** Book ID */
  id: number;
  /** Book title */
  title: string;
  /** Book author */
  author: string;
  /** ISBN if available */
  isbn?: string;
  /** Publication year */
  publicationYear?: string;
  /** Page count */
  pageCount?: number;
  /** Book description */
  description?: string;
  /** Cover image filename (relative to book folder) */
  coverImage?: string;
  /** OpenLibrary cover ID */
  coverImageId?: string;
  /** Google Books volume ID */
  googleBooksId?: string;
  /** Creation timestamp */
  createdAt: string;
  /** Last update timestamp */
  updatedAt: string;
  /** User's rating */
  rating?: number;
  /** Reading status */
  status?: 'to-read' | 'reading' | 'completed';
  /** Tags for categorization */
  tags?: string[];
}

/**
 * Sync configuration options
 */
export interface SyncConfig {
  /** Path to sync directory */
  syncPath: string;
  /** Whether to auto-sync on changes */
  autoSync: boolean;
  /** Sync interval in minutes */
  syncInterval: number;
  /** Conflict resolution strategy */
  conflictStrategy: 'ask' | 'local' | 'remote' | 'newest';
  /** Whether to sync deletions */
  syncDeletions: boolean;
}

/**
 * Sync operation options
 */
export interface SyncOptions {
  /** Force sync even if no changes detected */
  force?: boolean;
  /** Dry run - detect changes without applying */
  dryRun?: boolean;
  /** Only sync specific types */
  types?: ('book' | 'folder' | 'note')[];
  /** Custom conflict resolution for this sync */
  conflictResolution?: 'local' | 'remote' | 'skip';
}

/**
 * File sync operation result
 */
export interface FileSyncResult {
  /** Source path */
  source: string;
  /** Destination path */
  destination: string;
  /** Operation performed */
  operation: 'created' | 'updated' | 'deleted' | 'skipped';
  /** Whether operation succeeded */
  success: boolean;
  /** Error message if failed */
  error?: string;
}

/**
 * Sync progress event data
 */
export interface SyncProgress {
  /** Current phase of sync */
  phase: 'scanning' | 'comparing' | 'importing' | 'exporting' | 'deleting' | 'finalizing' | 'starting' | 'loading' | 'detecting' | 'conflicts' | 'updating' | 'completed' | 'error';
  /** Current item being processed */
  current?: string;
  /** Total items to process */
  total: number;
  /** Items processed so far */
  processed: number;
  /** Percentage complete */
  percentage: number;
  /** Progress value 0-100 */
  progress?: number;
  /** Progress message */
  message?: string;
}

/**
 * Additional types for sync system
 */
export enum ItemType {
  BOOK = 'book',
  FOLDER = 'folder', 
  NOTE = 'note'
}

export enum SyncDirection {
  IMPORT = 'import',
  EXPORT = 'export',
  BIDIRECTIONAL = 'bidirectional'
}

export enum ConflictResolution {
  USE_LOCAL = 'local',
  USE_REMOTE = 'remote',
  MERGE = 'merge',
  ASK_USER = 'ask'
}

/**
 * Conflict resolution result
 */
export interface ConflictResolutionResult {
  winner: ManifestItem;
  source: 'local' | 'remote';
  merged: boolean;
}

/**
 * Extended SyncResult with additional properties
 */
export interface ExtendedSyncResult extends SyncResult {
  /** Number of items imported */
  itemsImported?: number;
  /** Number of items exported */  
  itemsExported?: number;
  /** Duration of sync operation in milliseconds */
  duration?: number;
}

/**
 * Extended ManifestItem with additional properties
 */
export interface ExtendedManifestItem extends ManifestItem {
  /** Last update timestamp */
  updated_at?: string;
  /** Device that last modified this item */
  device_id?: string;
  /** Additional metadata */
  metadata?: Record<string, any>;
}